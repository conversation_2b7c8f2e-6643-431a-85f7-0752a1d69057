root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
args_bin = []
bin = "./tmp/main"
cmd = "go build -o ./tmp/main ./cmd/ozow-app/ozow.go"
delay = 1000
exclude_dir = ["tmp", "vendor", "testdata", ".git", "node_modules"]
exclude_file = ["README.md"]
exclude_regex = ["_test.go"]
exclude_unchanged = true
follow_symlink = false
full_bin = ""
include_dir = []
include_ext = ["go", "tpl", "templ", "html", "env"]
include_file = [".env"]
kill_delay = "0s"
log = "build-errors.log"
poll = false
poll_interval = 0
post_cmd = []
pre_cmd = [
    "tailwindcss -i ./input.css -o ./assets/css/style.css  --minify",
    "bunx terser node_modules/htmx.org/dist/htmx.min.js -o assets/js/htmx.js",
    "templ generate",
]
rerun = false
rerun_delay = 500
send_interrupt = false
stop_on_error = false

[color]
app = "white"
main = "yellow"
watcher = "magenta"
build = "blue"
runner = "green"

[log]
main_only = false
time = false

[misc]
clean_on_exit = false

[screen]
clear_on_rebuild = false
keep_scroll = true
