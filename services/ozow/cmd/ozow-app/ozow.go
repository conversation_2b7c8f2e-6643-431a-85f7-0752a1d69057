package main

import (
	"aps/lib/amplifin/utils"
	"aps/services/ozow/assets"
	"aps/services/ozow/internal/handlers"
	"log"
	"net/http"
	"os"

	"github.com/joho/godotenv"
)

func init() {
	err := godotenv.Load(".env")
	if err != nil {
		log.Println("Prod environment")
	}
}

func main() {
	URL := os.Getenv("URL")
	mux := http.NewServeMux()

	//CSS and JS to be cached
	for path, handler := range map[string]http.Handler{
		utils.AddPre(utils.AddPre("/css/", URL), "GET "): http.StripPrefix(utils.AddPre("/", URL), http.FileServer(http.FS(assets.CSS))),
		utils.AddPre(utils.AddPre("/js/", URL), "GET "):  http.StripPrefix(utils.AddPre("/", URL), http.FileServer(http.FS(assets.JS))),
	} {
		// Fix the malformed path by adding leading slash if missing
		fixedPath := path
		if len(path) > 4 && path[3] == ' ' && path[4] != '/' {
			fixedPath = path[:4] + "/" + path[4:]
		} else if len(path) > 5 && path[4] == ' ' && path[5] != '/' {
			fixedPath = path[:5] + "/" + path[5:]
		}
		mux.Handle(fixedPath, utils.CacheHandler(handler))
		log.Println("Registered handler for path", fixedPath)
	}
	//

	// Authentication routes
	loginGetPath := utils.AddPre(utils.AddPre("/login", URL), "GET ")
	if len(loginGetPath) > 4 && loginGetPath[3] == ' ' && loginGetPath[4] != '/' {
		loginGetPath = loginGetPath[:4] + "/" + loginGetPath[4:]
	}
	mux.HandleFunc(loginGetPath, handlers.LoginHandler)

	loginPostPath := utils.AddPre(utils.AddPre("/login", URL), "POST ")
	if len(loginPostPath) > 5 && loginPostPath[4] == ' ' && loginPostPath[5] != '/' {
		loginPostPath = loginPostPath[:5] + "/" + loginPostPath[5:]
	}
	mux.HandleFunc(loginPostPath, handlers.LoginPostHandler)

	logoutPath := utils.AddPre(utils.AddPre("/logout", URL), "POST ")
	if len(logoutPath) > 5 && logoutPath[4] == ' ' && logoutPath[5] != '/' {
		logoutPath = logoutPath[:5] + "/" + logoutPath[5:]
	}
	mux.HandleFunc(logoutPath, handlers.LogoutHandler)

	for path, handler := range map[string]http.HandlerFunc{
		utils.AddPre(utils.AddPre("/PaymentRequest", URL), "POST "): handlers.CreatePaymentRequest,
		utils.AddPre(utils.AddPre("/Refund", URL),
			"POST "): handlers.SubmitRefund,
		utils.AddPre(utils.AddPre("/Notify", URL), "POST "): handlers.Notify,

		//pages
		utils.AddPre(utils.AddPre("/pay", URL), "POST "):    handlers.WebPaymentRequest,
		utils.AddPre(utils.AddPre("/success", URL), "GET "): handlers.Success,
		utils.AddPre(utils.AddPre("/cancel", URL), "GET "):  handlers.Cancel,
		utils.AddPre(utils.AddPre("/error", URL), "GET "):   handlers.Error,
		utils.AddPre(utils.AddPre("/home", URL), "GET "):    handlers.Index,
		utils.AddPre(utils.AddPre("/reset", URL), "GET "):   handlers.Reset,
	} {
		// Fix the malformed path by adding leading slash if missing
		fixedPath := path
		if len(path) > 4 && path[3] == ' ' && path[4] != '/' {
			fixedPath = path[:4] + "/" + path[4:]
		} else if len(path) > 5 && path[4] == ' ' && path[5] != '/' {
			fixedPath = path[:5] + "/" + path[5:]
		}
		mux.HandleFunc(fixedPath, handler)
		log.Println("Registered handler for path", fixedPath)
	}
	err := http.ListenAndServe(":3000", mux)
	log.Println("Starting server on port 3000")
	if err != nil {
		log.Fatal(err)
	}

}
