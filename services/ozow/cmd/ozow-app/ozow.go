package main

import (
	"aps/lib/amplifin/utils"
	"aps/services/ozow/assets"
	"aps/services/ozow/internal/handlers"
	"log"
	"net/http"
	"os"

	"github.com/joho/godotenv"
)

func init() {
	err := godotenv.Load(".env")
	if err != nil {
		log.Println("Prod environment")
	}
}

// fixRoute fixes the malformed route patterns from utils.AddPre
func fixRoute(route string) string {
	// Convert "GET ozow/path" to "GET /ozow/path" or "POST ozow/path" to "POST /ozow/path"
	if len(route) > 4 && route[3] == ' ' && route[4] != '/' {
		return route[:4] + "/" + route[4:]
	}
	if len(route) > 5 && route[4] == ' ' && route[5] != '/' {
		return route[:5] + "/" + route[5:]
	}
	return route
}

func main() {
	URL := os.Getenv("URL")
	mux := http.NewServeMux()

	//CSS and JS to be cached
	for path, handler := range map[string]http.Handler{
		fixRoute(utils.AddPre(utils.AddPre("/css/", URL), "GET ")): http.StripPrefix(utils.AddPre("/", URL), http.FileServer(http.FS(assets.CSS))),
		fixRoute(utils.AddPre(utils.AddPre("/js/", URL), "GET ")):  http.StripPrefix(utils.AddPre("/", URL), http.FileServer(http.FS(assets.JS))),
	} {
		mux.Handle(path, utils.CacheHandler(handler))
		log.Println("Registered handler for path", path)
	}
	//

	// Authentication routes
	mux.HandleFunc(fixRoute(utils.AddPre(utils.AddPre("/login", URL), "GET ")), handlers.LoginHandler)
	mux.HandleFunc(fixRoute(utils.AddPre(utils.AddPre("/login", URL), "POST ")), handlers.LoginPostHandler)
	mux.HandleFunc(fixRoute(utils.AddPre(utils.AddPre("/logout", URL), "POST ")), handlers.LogoutHandler)

	for path, handler := range map[string]http.HandlerFunc{
		fixRoute(utils.AddPre(utils.AddPre("/PaymentRequest", URL), "POST ")): handlers.CreatePaymentRequest,
		fixRoute(utils.AddPre(utils.AddPre("/Refund", URL),
			"POST ")): handlers.SubmitRefund,
		fixRoute(utils.AddPre(utils.AddPre("/Notify", URL), "POST ")): handlers.Notify,

		//pages
		fixRoute(utils.AddPre(utils.AddPre("/pay", URL), "POST ")):    handlers.WebPaymentRequest,
		fixRoute(utils.AddPre(utils.AddPre("/success", URL), "GET ")): handlers.Success,
		fixRoute(utils.AddPre(utils.AddPre("/cancel", URL), "GET ")):  handlers.Cancel,
		fixRoute(utils.AddPre(utils.AddPre("/error", URL), "GET ")):   handlers.Error,
		fixRoute(utils.AddPre(utils.AddPre("/home", URL), "GET ")):    handlers.Index,
		fixRoute(utils.AddPre(utils.AddPre("/reset", URL), "GET ")):   handlers.Reset,
	} {
		mux.HandleFunc(path, handler)
		log.Println("Registered handler for path", path)
	}

	// Root route - redirect to /ozow/home
	mux.HandleFunc("GET /", func(w http.ResponseWriter, r *http.Request) {
		http.Redirect(w, r, "/"+URL+"/home", http.StatusFound)
	})

	// Ozow root route - redirect to /ozow/home
	mux.HandleFunc("GET /"+URL, func(w http.ResponseWriter, r *http.Request) {
		http.Redirect(w, r, "/"+URL+"/home", http.StatusFound)
	})

	err := http.ListenAndServe(":3000", mux)
	log.Println("Starting server on port 3000")
	if err != nil {
		log.Fatal(err)
	}

}
