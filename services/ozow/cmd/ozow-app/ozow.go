package main

import (
	"aps/lib/amplifin/utils"
	"aps/services/ozow/assets"
	"aps/services/ozow/internal/handlers"
	"log"
	"net/http"
	"os"

	"github.com/joho/godotenv"
)

func init() {
	err := godotenv.Load(".env")
	if err != nil {
		log.Println("Prod environment")
	}
}

// buildRoute constructs proper HTTP route patterns for Go's ServeMux
func buildRoute(method, path, urlPrefix string) string {
	return method + " /" + urlPrefix + path
}

func main() {
	URL := os.Getenv("URL")
	mux := http.NewServeMux()

	//CSS and JS to be cached
	for path, handler := range map[string]http.Handler{
		buildRoute("GET", "/css/", URL): http.StripPrefix("/"+URL, http.FileServer(http.FS(assets.CSS))),
		buildRoute("GET", "/js/", URL):  http.StripPrefix("/"+URL, http.FileServer(http.FS(assets.JS))),
	} {
		mux.Handle(path, utils.CacheHandler(handler))
		log.Println("Registered handler for path", path)
	}
	//

	// Authentication routes
	mux.HandleFunc(buildRoute("GET", "/login", URL), handlers.LoginHandler)
	mux.HandleFunc(buildRoute("POST", "/login", URL), handlers.LoginPostHandler)
	mux.HandleFunc(buildRoute("POST", "/logout", URL), handlers.LogoutHandler)

	for path, handler := range map[string]http.HandlerFunc{
		buildRoute("POST", "/PaymentRequest", URL): handlers.CreatePaymentRequest,
		buildRoute("POST", "/Refund", URL):         handlers.SubmitRefund,
		buildRoute("POST", "/Notify", URL):         handlers.Notify,

		//pages
		buildRoute("POST", "/pay", URL):    handlers.WebPaymentRequest,
		buildRoute("GET", "/success", URL): handlers.Success,
		buildRoute("GET", "/cancel", URL):  handlers.Cancel,
		buildRoute("GET", "/error", URL):   handlers.Error,
		buildRoute("GET", "/home", URL):    handlers.Index,
		buildRoute("GET", "/reset", URL):   handlers.Reset,
	} {
		mux.HandleFunc(path, handler)
		log.Println("Registered handler for path", path)
	}

	// Root route - redirect to /ozow/home
	mux.HandleFunc("GET /", func(w http.ResponseWriter, r *http.Request) {
		http.Redirect(w, r, "/"+URL+"/home", http.StatusFound)
	})

	// Ozow root route - redirect to /ozow/home
	mux.HandleFunc("GET /"+URL, func(w http.ResponseWriter, r *http.Request) {
		http.Redirect(w, r, "/"+URL+"/home", http.StatusFound)
	})

	err := http.ListenAndServe(":3000", mux)
	log.Println("Starting server on port 3000")
	if err != nil {
		log.Fatal(err)
	}

}
