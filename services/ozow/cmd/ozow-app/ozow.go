package main

import (
	"aps/lib/amplifin/utils"
	"aps/services/ozow/assets"
	"aps/services/ozow/internal/handlers"
	"log"
	"net/http"
	"os"

	"github.com/joho/godotenv"
)

func init() {
	err := godotenv.Load(".env")
	if err != nil {
		log.Println("Prod environment")
	}
}

func main() {
	URL := os.Getenv("URL")
	mux := http.NewServeMux()

	//CSS and JS to be cached
	cssPath := "GET /" + URL + "/css/"
	jsPath := "GET /" + URL + "/js/"
	mux.Handle(cssPath, utils.CacheHandler(http.StripPrefix("/"+URL, http.FileServer(http.FS(assets.CSS)))))
	mux.Handle(jsPath, utils.CacheHandler(http.StripPrefix("/"+URL, http.FileServer(http.FS(assets.JS)))))
	log.Println("Registered handler for path", cssPath)
	log.Println("Registered handler for path", jsPath)

	// Authentication routes
	loginGetPath := "GET /" + URL + "/login"
	loginPostPath := "POST /" + URL + "/login"
	logoutPath := "POST /" + URL + "/logout"
	mux.HandleFunc(loginGetPath, handlers.LoginHandler)
	mux.HandleFunc(loginPostPath, handlers.LoginPostHandler)
	mux.HandleFunc(logoutPath, handlers.LogoutHandler)
	log.Println("Registered handler for path", loginGetPath)
	log.Println("Registered handler for path", loginPostPath)
	log.Println("Registered handler for path", logoutPath)

	// API routes
	paymentRequestPath := "POST /" + URL + "/PaymentRequest"
	refundPath := "POST /" + URL + "/Refund"
	notifyPath := "POST /" + URL + "/Notify"
	payPath := "POST /" + URL + "/pay"
	mux.HandleFunc(paymentRequestPath, handlers.CreatePaymentRequest)
	mux.HandleFunc(refundPath, handlers.SubmitRefund)
	mux.HandleFunc(notifyPath, handlers.Notify)
	mux.HandleFunc(payPath, handlers.WebPaymentRequest)
	log.Println("Registered handler for path", paymentRequestPath)
	log.Println("Registered handler for path", refundPath)
	log.Println("Registered handler for path", notifyPath)
	log.Println("Registered handler for path", payPath)

	// Page routes
	successPath := "GET /" + URL + "/success"
	cancelPath := "GET /" + URL + "/cancel"
	errorPath := "GET /" + URL + "/error"
	homePath := "GET /" + URL + "/home"
	resetPath := "GET /" + URL + "/reset"
	mux.HandleFunc(successPath, handlers.Success)
	mux.HandleFunc(cancelPath, handlers.Cancel)
	mux.HandleFunc(errorPath, handlers.Error)
	mux.HandleFunc(homePath, handlers.Index)
	mux.HandleFunc(resetPath, handlers.Reset)
	log.Println("Registered handler for path", successPath)
	log.Println("Registered handler for path", cancelPath)
	log.Println("Registered handler for path", errorPath)
	log.Println("Registered handler for path", homePath)
	log.Println("Registered handler for path", resetPath)

	// Root route - redirect to /ozow/home
	mux.HandleFunc("GET /", func(w http.ResponseWriter, r *http.Request) {
		http.Redirect(w, r, "/"+URL+"/home", http.StatusFound)
	})

	// Ozow root route - redirect to /ozow/home
	mux.HandleFunc("GET /"+URL, func(w http.ResponseWriter, r *http.Request) {
		http.Redirect(w, r, "/"+URL+"/home", http.StatusFound)
	})

	log.Println("Registered all routes with URL prefix:", URL)
	err := http.ListenAndServe(":3000", mux)
	log.Println("Starting server on port 3000")
	if err != nil {
		log.Fatal(err)
	}

}
