# Sample Go Application

This is a simple Go application that demonstrates how to create a basic web server using the Go programming language.It is part of the APS mono repo and used as a base to create add a services to the mono repo

## Table of Contents

- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Steps](#adding-a-service)
- [Usage](#usage)
- [License](#license)

## Getting Started

### Prerequisites

- Before running this application, you need to have Go installed on your system. If you haven't installed Go, you can download and install it from the [official Go website](https://go.dev/dl/).

- Make sure you have `make` installed using your package manager

- Make sure you have `air` installed from [here](https://github.com/cosmtrek/air).

- Make sure you have `podman` installed using your package manager

- A project, if there isn't an existing project for your service create it.

### Adding a service

1. Clone the aps-mono repo `*****************:aps-holdings/aps-mono.git` to your machine
2. On bitbucket create a fork of this repo on bitbucket and git it an appropriate name and select the correct project
3. Copy the ssh clone url
4. In the aps-mono repo root run `git submodule add <url> src/services/<service-name>`
5. Copy and rename `ozow.Containerfile`, `ozow-dev.Containerfile` & `ozow.Makefile` to your service name and replace instances of `ozow` inside the files
6. In a separate folder clone the aps-cicd repo `*****************:aps-holdings/ozow-cicd.git`.
7. In the root of the folder `cd` into `src/dev/` and create a folder for your project if one doesn't exist
8. Copy the contents `./src/dev/ozow/ozow-app` to your project folder.
9. Rename the values in the markdown files to the appropriate ones for your service (you can use your editor to open your service cicd folder and use find and replace the word ozow with the service name).

## Usage

1. Run the Go application:

   ```bash
   make start
   ```

2. Open a web browser and navigate to [here](http://localhost:3000/).

You should see a simple `Hello, World!` message displayed in your web browser.

---

## License

This project is the property of APS Holdings and is not licensed for public use. Unauthorized use, reproduction, or distribution is prohibited.
