# Use a minimal base image
FROM docker.io/golang:1.23 AS builder

ENV CGO_ENABLED=0
ENV GOOS=linux

WORKDIR /app

COPY go.mod go.sum ./

COPY lib/ ./lib/

COPY services/ozow/ ./services/ozow/

RUN go mod download && go mod tidy  && go mod verify

RUN go build -v -ldflags "-w -s" -o /usr/local/bin/app ./services/ozow/cmd/ozow-app/ozow.go

# Create a minimal final image
FROM gcr.io/distroless/static-debian12

ENV URL=""
ENV VAULT_URL=""
ENV VAULT_TOKEN=""

COPY --from=builder /usr/local/bin/app /usr/local/bin/app

EXPOSE 3000
CMD ["app"]