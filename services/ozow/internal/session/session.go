package session

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/go-redis/redis/v8"
)

type SessionManager struct {
	client *redis.Client
}

func NewSessionManager() *SessionManager {
	redisAddr := os.Getenv("REDIS_ADDR")
	redisPassword := os.Getenv("REDIS_PASSWORD")
	redisDB := 0

	if redisAddr == "" {
		redisAddr = "localhost:6379"
	}

	rdb := redis.NewClient(&redis.Options{
		Addr:     redisAddr,
		Password: redisPassword,
		DB:       redisDB,
	})

	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		log.Printf("Warning: Could not connect to Redis: %v", err)
		log.Println("Session management will not work properly without Redis")
	} else {
		log.Println("Successfully connected to Redis for session management")
	}

	return &SessionManager{client: rdb}
}

const (
	sessionCookieName = "ozow_session_id"
	sessionDuration   = 24 * time.Hour // 24 hours
)

func generateSessionID() string {
	return fmt.Sprintf("ozow_%d_%d", time.Now().UnixNano(), time.Now().UnixMicro())
}

func (sm *SessionManager) CreateSession(w http.ResponseWriter, userID string) error {
	sessionID := generateSessionID()
	ctx := context.Background()

	// Store session in Redis
	err := sm.client.Set(ctx, sessionID, userID, sessionDuration).Err()
	if err != nil {
		log.Printf("Redis error setting session: %v", err)
		return fmt.Errorf("failed to create session: %w", err)
	}

	http.SetCookie(w, &http.Cookie{
		Name:     sessionCookieName,
		Value:    sessionID,
		Path:     "/",
		Expires:  time.Now().Add(sessionDuration),
		HttpOnly: true,
		Secure:   false,
		SameSite: http.SameSiteLaxMode,
	})

	log.Printf("Created session for user: %s", userID)
	return nil
}

func (sm *SessionManager) GetUserID(r *http.Request) (string, error) {
	cookie, err := r.Cookie(sessionCookieName)
	if err != nil {
		if err == http.ErrNoCookie {
			return "", nil // No session cookie
		}
		return "", fmt.Errorf("error getting session cookie: %w", err)
	}

	sessionID := cookie.Value
	ctx := context.Background()

	userID, err := sm.client.Get(ctx, sessionID).Result()
	if err != nil {
		if err == redis.Nil {
			return "", nil
		}
		log.Printf("Redis error getting session: %v", err)
		return "", fmt.Errorf("failed to get session: %w", err)
	}

	return userID, nil
}

func (sm *SessionManager) DeleteSession(w http.ResponseWriter, r *http.Request) error {
	cookie, err := r.Cookie(sessionCookieName)
	if err == nil {
		ctx := context.Background()
		sm.client.Del(ctx, cookie.Value)
		log.Printf("Deleted session: %s", cookie.Value)
	}

	http.SetCookie(w, &http.Cookie{
		Name:     sessionCookieName,
		Value:    "",
		Path:     "/",
		Expires:  time.Unix(0, 0),
		HttpOnly: true,
		Secure:   false,
		SameSite: http.SameSiteLaxMode,
	})

	return nil
}

func (sm *SessionManager) IsAuthenticated(r *http.Request) bool {
	userID, err := sm.GetUserID(r)
	return err == nil && userID != ""
}