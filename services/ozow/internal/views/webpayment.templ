package views

import (
	"aps/services/ozow/internal/components"
	"aps/services/ozow/internal/models"
	"fmt"
)

const (
	page         = "Pay"
	style        = "/ozow/css/style.css"
	payment_link = "/ozow/pay"
)

templ Index() {
	<!DOCTYPE html>
	<html lang="en" data-theme="light">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>Pay</title>
			<link
				rel="stylesheet"
				type="text/css"
				href={ models.STYLE }
			/>
			<link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💳</text></svg>"/>
		</head>
		<body>
			<script type="module" src={ models.JS }></script>
			@components.Header(page)
			<div class="flex min-h-screen items-center justify-center bg-opacity-80">
				<div class="card glass w-96 pt-6">
					<div class="items-center justify-center  text-center text-3xl">
						🪙
					</div>
					<div class="card-body grid place-items-center">
						<form
							id="payment-form"
							hx-post={ payment_link }
							hx-target="#toast"
							hx-indicator="#indicator"
							hx-swap="innerHTML"
							class="grid place-items-center"
						>
							<div id="toast" class="card-body place-items-center w-80">
								<h2 class="card-title pb-3">Make Payment</h2>
								<input name="idNumber" id="idNumber" type="text" placeholder="ID Number" class="input focus:outline-none"/>
								<input name="amount" id="amount" type="number" placeholder="Amount" class="input focus:outline-none"/>
							</div>
							<button
								type="submit"
								class="content-center btn btn-primary btn-wide"
							>
								Pay
							</button>
							<span id="indicator" class="icon-[line-md--loading-twotone-loop] text-2xl htmx-indicator"></span>
						</form>
					</div>
				</div>
			</div>
		</body>
	</html>
}

templ IndexWithUser(user *models.User) {
	<!DOCTYPE html>
	<html lang="en" data-theme="light">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>Pay - Welcome { user.FullName }</title>
			<link
				rel="stylesheet"
				type="text/css"
				href={ models.STYLE }
			/>
			<link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💳</text></svg>"/>
		</head>
		<body>
			<script type="module" src={ models.JS }></script>
			@HeaderWithUser(page, user)
			<div class="flex min-h-screen items-center justify-center bg-opacity-80">
				<div class="card glass w-96 pt-6">
					<div class="items-center justify-center text-center text-3xl">
						🪙
					</div>
					<div class="card-body grid place-items-center">
						<!-- User Information Section -->
						<div class="w-full mb-6 p-4 bg-base-200 rounded-lg">
							<h3 class="text-lg font-semibold mb-2">Account Information</h3>
							<div class="space-y-2 text-sm">
								<p><span class="font-medium">Name:</span> { user.FullName }</p>
								if user.EmployeeCode != "" {
									<p><span class="font-medium">Employee Code:</span> { user.EmployeeCode }</p>
								}
								if user.Employer != "" {
									<p><span class="font-medium">Employer:</span> { user.Employer }</p>
								}
								if user.Department != "" {
									<p><span class="font-medium">Department:</span> { user.Department }</p>
								}
								if user.SalaryAmount > 0 {
									<p><span class="font-medium">Salary:</span> R{ fmt.Sprintf("%.2f", user.SalaryAmount) }</p>
								}
							</div>
						</div>

						<!-- Payment Form -->
						<form
							id="payment-form"
							hx-post={ payment_link }
							hx-target="#toast"
							hx-indicator="#indicator"
							hx-swap="innerHTML"
							class="grid place-items-center w-full"
						>
							<div id="toast" class="card-body place-items-center w-80">
								<h2 class="card-title pb-3">Make Payment</h2>
								<!-- Pre-fill ID Number for authenticated user -->
								<input
									name="idNumber"
									id="idNumber"
									type="text"
									placeholder="ID Number"
									value={ user.IdentificationDetail }
									class="input focus:outline-none"
									readonly
								/>
								<input name="amount" id="amount" type="number" placeholder="Amount" class="input focus:outline-none"/>
							</div>
							<button
								type="submit"
								class="content-center btn btn-primary btn-wide"
							>
								Pay
							</button>
							<span id="indicator" class="icon-[line-md--loading-twotone-loop] text-2xl htmx-indicator"></span>
						</form>
					</div>
				</div>
			</div>
		</body>
	</html>
}