package views

import (
	"aps/services/ozow/internal/components"
	"aps/services/ozow/internal/models"
)

type LoginData struct {
	Error string
}

templ Login(data LoginData) {
	<!DOCTYPE html>
	<html lang="en" data-theme="light">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>Login - Ozow Payment</title>
			<link
				rel="stylesheet"
				type="text/css"
				href={ models.STYLE }
			/>
			<link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🔐</text></svg>"/>
		</head>
		<body>
			<script type="module" src={ models.JS }></script>
			@components.Header("Login")
			<div class="flex min-h-screen items-center justify-center bg-opacity-80">
				<div class="card glass w-96 pt-6">
					<div class="items-center justify-center text-center text-3xl">
						🔐
					</div>
					<div class="card-body grid place-items-center">
						<h2 class="text-2xl font-bold text-center mb-6">Login to Ozow</h2>
						if data.Error != "" {
							<div class="alert alert-error mb-4">
								<span>{ data.Error }</span>
							</div>
						}
						<form 
							hx-post="/ozow/login" 
							hx-target="#login-container" 
							hx-swap="outerHTML" 
							class="w-full space-y-4"
							id="login-container"
						>
							<div class="form-control">
								<label class="label" for="idNumber">
									<span class="label-text">ID Number</span>
								</label>
								<input
									type="text"
									id="idNumber"
									name="idNumber"
									placeholder="Enter your ID number"
									class="input input-bordered w-full focus:outline-none"
									required
								/>
							</div>
							<div class="form-control">
								<label class="label" for="password">
									<span class="label-text">Password</span>
								</label>
								<input
									type="password"
									id="password"
									name="password"
									placeholder="Enter your password"
									class="input input-bordered w-full focus:outline-none"
									required
								/>
							</div>
							<button
								type="submit"
								class="btn btn-primary w-full"
							>
								Sign In
							</button>
						</form>
					</div>
				</div>
			</div>
		</body>
	</html>
}

templ LoginForm(data LoginData) {
	<div id="login-container">
		<h2 class="text-2xl font-bold text-center mb-6">Login to Ozow</h2>
		if data.Error != "" {
			<div class="alert alert-error mb-4">
				<span>{ data.Error }</span>
			</div>
		}
		<form 
			hx-post="/ozow/login" 
			hx-target="#login-container" 
			hx-swap="outerHTML" 
			class="w-full space-y-4"
		>
			<div class="form-control">
				<label class="label" for="idNumber">
					<span class="label-text">ID Number</span>
				</label>
				<input
					type="text"
					id="idNumber"
					name="idNumber"
					placeholder="Enter your ID number"
					class="input input-bordered w-full focus:outline-none"
					required
				/>
			</div>
			<div class="form-control">
				<label class="label" for="password">
					<span class="label-text">Password</span>
				</label>
				<input
					type="password"
					id="password"
					name="password"
					placeholder="Enter your password"
					class="input input-bordered w-full focus:outline-none"
					required
				/>
			</div>
			<button
				type="submit"
				class="btn btn-primary w-full"
			>
				Sign In
			</button>
		</form>
	</div>
}