package models

import (
	aps_vault_models "aps/lib/generic/vault/models"
	"database/sql"
	"encoding/json"
)

type PaymentReq struct {
	SiteCode                string  `json:"siteCode,omitempty"`
	CountryCode             string  `json:"countryCode,omitempty"`
	CurrencyCode            string  `json:"currencyCode,omitempty"`
	Amount                  float64 `json:"amount,omitempty"`
	TransactionReference    string  `json:"transactionReference,omitempty"`
	BankReference           string  `json:"bankReference,omitempty"`
	Optional1               string  `json:"optional1,omitempty"`
	Optional2               string  `json:"optional2,omitempty"`
	Optional3               string  `json:"optional3,omitempty"`
	Optional4               string  `json:"optional4,omitempty"`
	Optional5               string  `json:"optional5,omitempty"`
	Customer                string  `json:"customer,omitempty"`
	CancelURL               string  `json:"cancelUrl,omitempty"`
	ErrorURL                string  `json:"errorUrl,omitempty"`
	SuccessURL              string  `json:"successUrl,omitempty"`
	NotifyURL               string  `json:"notifyUrl,omitempty"`
	IsTest                  bool    `json:"isTest"`
	SelectedBankID          string  `json:"selectedBankId,omitempty"`
	BankAccountNumber       string  `json:"bankAccountNumber,omitempty"`
	BranchCode              string  `json:"branchCode,omitempty"`
	BankAccountName         string  `json:"bankAccountName,omitempty"`
	PayeeDisplayName        string  `json:"payeeDisplayName,omitempty"`
	ExpiryDateUtc           string  `json:"expiryDateUtc,omitempty"`
	AllowVariableAmount     bool    `json:"allowVariableAmount,omitempty"`
	VariableAmountMin       int     `json:"variableAmountMin,omitempty"`
	VariableAmountMax       int     `json:"variableAmountMax,omitempty"`
	CustomerIdentifier      string  `json:"customerIdentifier,omitempty"`
	CustomerCellphoneNumber string  `json:"customerCellphoneNumber,omitempty"`
	HashCheck               string  `json:"hashCheck,omitempty"`
}

type Notify struct {
	SiteCode             string  `json:"SiteCode"`
	TransactionID        string  `json:"TransactionId"`
	TransactionReference string  `json:"TransactionReference"`
	Amount               float64 `json:"Amount"`
	Status               string  `json:"Status"`
	Optional1            string  `json:"Optional1"`
	Optional2            string  `json:"Optional2"`
	Optional3            string  `json:"Optional3"`
	Optional4            string  `json:"Optional4"`
	Optional5            string  `json:"Optional5"`
	CurrencyCode         string  `json:"CurrencyCode"`
	Hash                 string  `json:"Hash"`
	SubStatus            string  `json:"SubStatus"`
	MaskedAccountNumber  string  `json:"MaskedAccountNumber"`
	BankName             string  `json:"BankName"`
	SmartIndicators      string  `json:"SmartIndicators"`
}

type MyCred struct {
	Apikey     string `json:"apikey,omitempty,omitempty"`
	Privatekey string `json:"privatekey,omitempty,omitempty"`
}

type AuthCred struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int64  `json:"expires_in"`
	TokenType   string `json:"token_type"`
}

type VaultAuth struct {
	aps_vault_models.VaultCommon
	Data AuthCred
}

type MyVaultCred struct {
	aps_vault_models.VaultCommon
	Data MyCred
}

const (
	STYLE = "/ozow/css/style.css"
	JS    = "/ozow/js/htmx.js"
)

type ClientDetails struct {
	ClientKey               sql.NullString `json:"client_key"`
	IdentificationDetail    sql.NullString `json:"identification_detail"`
	Employer                sql.NullString `json:"employer"`
	Department              sql.NullString `json:"department"`
	EmployeeCode            sql.NullString `json:"employee_code"`
	EngagementDate          sql.NullString `json:"engagement_date"`
	Payday                  sql.NullString `json:"payday"`
	PaydayFrequency         sql.NullString `json:"payday_frequency"`
	SalaryAmount            float64        `json:"salary_amount"`
	EmploymentType          sql.NullString `json:"employment_type"`
	FirstName               sql.NullString `json:"first_name"`
	Surname                 sql.NullString `json:"surname"`
	AdditionalNames         sql.NullString `json:"additional_names"`
	KnownAs                 sql.NullString `json:"known_as"`
	MaritalStatus           sql.NullString `json:"marital_status"`
	ResidentialBuilding     sql.NullString `json:"residential_building"`
	ResidentialAddressLine1 sql.NullString `json:"residential_address_line_1"`
	ResidentialAddressLine2 sql.NullString `json:"residential_address_line_2"`
	ResidentialAddressLine3 sql.NullString `json:"residential_address_line_3"`
	ResidentialAddressLine4 sql.NullString `json:"residential_address_line_4"`
	ResidentialProvince     sql.NullString `json:"residential_province"`
	ResidentialPostalCode   sql.NullString `json:"residential_postal_code"`
	WorkBuilding            sql.NullString `json:"work_building"`
	WorkAddressLine1        sql.NullString `json:"work_address_line_1"`
	WorkAddressLine2        sql.NullString `json:"work_address_line_2"`
	WorkAddressLine3        sql.NullString `json:"work_address_line_3"`
	WorkAddressLine4        sql.NullString `json:"work_address_line_4"`
	WorkProvince            sql.NullString `json:"work_province"`
	WorkPostalCode          sql.NullString `json:"work_postal_code"`
	MobileNumber            sql.NullString `json:"mobile_number"`
	EmailAddress            sql.NullString `json:"email_address"`
	AccountKey              sql.NullString `json:"account_key"`
	AccountNumber           sql.NullString `json:"account_number"`
	ContractKey             sql.NullString `json:"contract_key"`
	ContractHash            sql.NullString `json:"contract_hash"`
	ContractStatus          sql.NullString `json:"contract_status"`
	TrueBalance             sql.NullString `json:"true_balance"`
	PtpBalance              sql.NullString `json:"ptp_balance"`
	SettlementValue         sql.NullString `json:"settlement_value"`
	ActivationDate          sql.NullString `json:"activation_date"`
	FullContractValue       sql.NullString `json:"full_contract_value"`
	InstallmentValue        sql.NullString `json:"installment_value"`
}

type PaymentRes struct {
	PaymentRequestID string     `json:"paymentRequestId"`
	URL              string     `json:"url"`
	ErrorMessage     NullString `json:"errorMessage"`
}

type NullString struct {
	sql.NullString
}

func (ns *NullString) UnmarshalJSON(data []byte) error {
	if string(data) == "null" {
		ns.Valid = false
		return nil
	}

	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}

	ns.String = s
	ns.Valid = true
	return nil
}

type User struct {
	ID                   string
	IdentificationDetail string
	FirstName            string
	Surname              string
	FullName             string
	Employer             string
	Department           string
	EmployeeCode         string
	SalaryAmount         float64
	ClientKey            string
}