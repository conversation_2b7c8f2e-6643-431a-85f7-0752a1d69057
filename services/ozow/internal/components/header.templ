package components

templ Header(Page string) {
	<div class="navbar bg-primary text-primary-content">
		<div class="navbar-start">
			<button class="text-xl btn btn-ghost">
				<a class="icon-[line-md--phone-call-loop] text-2xl"></a>
			</button>
		</div>
		<div class="hidden navbar-center lg:flex">
			<ul class="px-1 menu menu-horizontal">
				<li><a href="/ozow/home">{ Page }</a></li>
			</ul>
		</div>
		<div class="navbar-end">
			<label class="flex cursor-pointer gap-2">
				<span class="icon-[line-md--sun-rising-loop] text-2xl"></span>
				<input type="checkbox" value="night" class="toggle theme-controller"/>
				<span class="icon-[line-md--moon-rising-alt-loop] text-2xl"></span>
			</label>
		</div>
	</div>
}
